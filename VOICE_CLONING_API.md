# Voice Cloning API

This API provides voice cloning functionality using the CSM-1B model from the `generator.py` module. It allows you to generate speech in a target voice by providing reference audio and text.

## Features

- **Voice Cloning**: Generate speech in any voice using reference audio
- **Multiple Input Methods**: Support for both base64 encoded audio and file uploads
- **Flexible Parameters**: Configurable temperature, top-k sampling, and audio length
- **Performance Metrics**: Returns generation time and audio duration information

## API Endpoints

### 1. Health Check
```
GET /health
```
Returns the health status of the API and whether the model is loaded.

**Response:**
```json
{
  "status": "healthy",
  "model_loaded": true
}
```

### 2. Voice Cloning (Base64)
```
POST /clone
```
<PERSON>lone voice using base64 encoded reference audio.

**Request Body:**
```json
{
  "text": "Text to generate speech for",
  "reference_text": "Text that corresponds to the reference audio",
  "reference_audio_base64": "base64_encoded_wav_audio",
  "speaker_id": 0,
  "temperature": 0.7,
  "topk": 30,
  "max_audio_length_ms": 90000
}
```

**Response:**
```json
{
  "success": true,
  "message": "Voice cloning successful",
  "audio_base64": "base64_encoded_generated_audio",
  "generation_time": 2.5,
  "audio_duration": 3.2
}
```

### 3. Voice Cloning (File Upload)
```
POST /clone-file
```
Clone voice using uploaded reference audio file.

**Form Data:**
- `text`: Text to generate speech for
- `reference_text`: Text that corresponds to the reference audio
- `reference_audio`: Reference audio file (WAV format)
- `speaker_id`: Speaker ID (default: 0)
- `temperature`: Sampling temperature (default: 0.7)
- `topk`: Top-k sampling parameter (default: 30)
- `max_audio_length_ms`: Maximum audio length in milliseconds (default: 90000)

**Response:**
Returns the generated audio file directly with performance metrics in headers.

## Usage

### Starting the API Server

```bash
python ap.py
```

The server will start on `http://localhost:8001`

### Using the API

#### Python Example (Base64 method)

```python
import requests
import base64

# Encode reference audio
with open("reference.wav", "rb") as f:
    reference_audio_base64 = base64.b64encode(f.read()).decode('utf-8')

# Make request
response = requests.post("http://localhost:8001/clone", json={
    "text": "Hello, this is a test of voice cloning.",
    "reference_text": "This is the reference text that matches the audio.",
    "reference_audio_base64": reference_audio_base64,
    "temperature": 0.7,
    "topk": 30
})

# Save generated audio
if response.json()["success"]:
    audio_data = base64.b64decode(response.json()["audio_base64"])
    with open("generated.wav", "wb") as f:
        f.write(audio_data)
```

#### Python Example (File upload method)

```python
import requests

# Prepare form data
form_data = {
    "text": "Hello, this is a test of voice cloning.",
    "reference_text": "This is the reference text that matches the audio.",
    "temperature": 0.7,
    "topk": 30
}

# Upload file
with open("reference.wav", "rb") as f:
    files = {"reference_audio": f}
    response = requests.post("http://localhost:8001/clone-file", 
                           data=form_data, files=files)

# Save generated audio
with open("generated.wav", "wb") as f:
    f.write(response.content)
```

#### cURL Example

```bash
# File upload method
curl -X POST "http://localhost:8001/clone-file" \
  -F "text=Hello, this is a test of voice cloning." \
  -F "reference_text=This is the reference text." \
  -F "reference_audio=@reference.wav" \
  -F "temperature=0.7" \
  -F "topk=30" \
  --output generated.wav
```

## Parameters

- **text**: The text you want to generate speech for
- **reference_text**: Text that corresponds to your reference audio (should match what's spoken in the reference audio)
- **reference_audio**: Reference audio file or base64 encoded audio
- **speaker_id**: Speaker identifier (default: 0)
- **temperature**: Controls randomness in generation (0.1-1.0, default: 0.7)
- **topk**: Top-k sampling parameter (1-50, default: 30)
- **max_audio_length_ms**: Maximum length of generated audio in milliseconds (default: 90000)

## Requirements

- Python 3.8+
- PyTorch
- torchaudio
- FastAPI
- All dependencies from the main project

## Tips for Best Results

1. **Reference Audio Quality**: Use clear, high-quality reference audio with minimal background noise
2. **Reference Text Accuracy**: Ensure the reference text exactly matches what's spoken in the reference audio
3. **Audio Length**: Use reference audio that's 3-10 seconds long for best results
4. **Temperature**: Lower values (0.3-0.5) for more consistent output, higher values (0.7-0.9) for more variation
5. **File Format**: WAV format is recommended for reference audio

## Example Script

Run the included example script to test the API:

```bash
python example_clone_usage.py
```

Make sure to have a reference audio file named `reference_audio.wav` in the same directory.

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `400`: Bad request (missing required parameters)
- `500`: Internal server error (model not loaded, generation failed)

Always check the `success` field in JSON responses when using the `/clone` endpoint.
