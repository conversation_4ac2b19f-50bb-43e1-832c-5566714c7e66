#!/usr/bin/env python3
"""
Example script demonstrating how to use the voice cloning API
"""

import requests
import base64
import json
import time
import os

# API endpoint
API_BASE_URL = "http://localhost:8001"

def encode_audio_file_to_base64(file_path: str) -> str:
    """Encode an audio file to base64"""
    with open(file_path, "rb") as f:
        audio_bytes = f.read()
    return base64.b64encode(audio_bytes).decode('utf-8')

def save_base64_audio(base64_str: str, output_path: str):
    """Save base64 encoded audio to file"""
    audio_bytes = base64.b64decode(base64_str)
    with open(output_path, "wb") as f:
        f.write(audio_bytes)

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    response = requests.get(f"{API_BASE_URL}/health")
    print(f"Health check response: {response.json()}")
    return response.status_code == 200

def test_voice_cloning_with_base64(reference_audio_path: str, reference_text: str, target_text: str):
    """Test voice cloning using base64 encoded audio"""
    print(f"\nTesting voice cloning with base64...")
    print(f"Reference audio: {reference_audio_path}")
    print(f"Reference text: {reference_text}")
    print(f"Target text: {target_text}")
    
    # Encode reference audio
    if not os.path.exists(reference_audio_path):
        print(f"Error: Reference audio file not found: {reference_audio_path}")
        return False
    
    reference_audio_base64 = encode_audio_file_to_base64(reference_audio_path)
    
    # Prepare request
    request_data = {
        "text": target_text,
        "reference_text": reference_text,
        "reference_audio_base64": reference_audio_base64,
        "speaker_id": 0,
        "temperature": 0.7,
        "topk": 30,
        "max_audio_length_ms": 10000  # 10 seconds max
    }
    
    # Make request
    start_time = time.time()
    response = requests.post(f"{API_BASE_URL}/clone", json=request_data)
    request_time = time.time() - start_time
    
    if response.status_code == 200:
        result = response.json()
        if result["success"]:
            print(f"✅ Voice cloning successful!")
            print(f"Generation time: {result['generation_time']:.2f}s")
            print(f"Audio duration: {result['audio_duration']:.2f}s")
            print(f"Total request time: {request_time:.2f}s")
            
            # Save generated audio
            output_path = f"cloned_voice_{int(time.time())}.wav"
            save_base64_audio(result["audio_base64"], output_path)
            print(f"Generated audio saved to: {output_path}")
            return True
        else:
            print(f"❌ Voice cloning failed: {result['message']}")
            return False
    else:
        print(f"❌ Request failed with status {response.status_code}: {response.text}")
        return False

def test_voice_cloning_with_file(reference_audio_path: str, reference_text: str, target_text: str):
    """Test voice cloning using file upload"""
    print(f"\nTesting voice cloning with file upload...")
    print(f"Reference audio: {reference_audio_path}")
    print(f"Reference text: {reference_text}")
    print(f"Target text: {target_text}")
    
    if not os.path.exists(reference_audio_path):
        print(f"Error: Reference audio file not found: {reference_audio_path}")
        return False
    
    # Prepare form data
    form_data = {
        "text": target_text,
        "reference_text": reference_text,
        "speaker_id": 0,
        "temperature": 0.7,
        "topk": 30,
        "max_audio_length_ms": 10000
    }
    
    # Prepare file
    with open(reference_audio_path, "rb") as f:
        files = {"reference_audio": (os.path.basename(reference_audio_path), f, "audio/wav")}
        
        # Make request
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/clone-file", data=form_data, files=files)
        request_time = time.time() - start_time
    
    if response.status_code == 200:
        print(f"✅ Voice cloning successful!")
        print(f"Total request time: {request_time:.2f}s")
        
        # Get generation info from headers
        generation_time = response.headers.get("X-Generation-Time")
        audio_duration = response.headers.get("X-Audio-Duration")
        if generation_time:
            print(f"Generation time: {float(generation_time):.2f}s")
        if audio_duration:
            print(f"Audio duration: {float(audio_duration):.2f}s")
        
        # Save generated audio
        output_path = f"cloned_voice_file_{int(time.time())}.wav"
        with open(output_path, "wb") as f:
            f.write(response.content)
        print(f"Generated audio saved to: {output_path}")
        return True
    else:
        print(f"❌ Request failed with status {response.status_code}: {response.text}")
        return False

def main():
    """Main function to run the examples"""
    print("Voice Cloning API Example")
    print("=" * 40)
    
    # Test health check first
    if not test_health_check():
        print("❌ API is not healthy. Make sure the server is running.")
        return
    
    # Example reference audio and texts
    # You'll need to provide your own reference audio file
    reference_audio_path = "reference_audio.wav"  # Replace with your audio file
    reference_text = "Hello, this is a sample reference text that matches the reference audio."
    target_text = "This is the text I want to generate in the cloned voice."
    
    print(f"\nNote: Please make sure you have a reference audio file at: {reference_audio_path}")
    print("The reference audio should be a clear recording of someone speaking the reference text.")
    
    # Test both methods if reference audio exists
    if os.path.exists(reference_audio_path):
        # Test base64 method
        success1 = test_voice_cloning_with_base64(reference_audio_path, reference_text, target_text)
        
        # Test file upload method
        success2 = test_voice_cloning_with_file(reference_audio_path, reference_text, target_text)
        
        if success1 or success2:
            print(f"\n✅ Voice cloning examples completed successfully!")
        else:
            print(f"\n❌ Voice cloning examples failed.")
    else:
        print(f"\n⚠️  Reference audio file not found. Please create '{reference_audio_path}' to test the API.")
        print("You can use any WAV file with clear speech that matches the reference text.")

if __name__ == "__main__":
    main()
