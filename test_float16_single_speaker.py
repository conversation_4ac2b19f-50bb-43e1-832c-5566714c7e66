#!/usr/bin/env python3
"""
Test script to verify float16 and single-speaker voice cloning modifications.
"""

import time
import torch
import torchaudio
from generator import Segment, load_csm_1b, generate_streaming_audio

def test_float16_support():
    """Test that the model loads with float16 instead of bfloat16."""
    print("Testing float16 support...")
    
    # Check if CUDA is available
    if not torch.cuda.is_available():
        print("❌ CUDA not available. This test requires a GPU.")
        return False
    
    # Check if bfloat16 is supported (we want to force float16 even if bfloat16 is supported)
    bf16_supported = torch.cuda.is_bf16_supported()
    print(f"GPU bfloat16 support: {bf16_supported}")
    
    try:
        print("Loading model with forced float16...")
        generator = load_csm_1b("cuda")
        
        # Check the model's dtype
        model_dtype = next(generator._model.parameters()).dtype
        print(f"Model loaded with dtype: {model_dtype}")
        
        if model_dtype == torch.float16:
            print("✅ Model successfully loaded with float16")
            return True, generator
        else:
            print(f"❌ Model loaded with {model_dtype} instead of float16")
            return False, None
            
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False, None

def test_single_speaker_voice_cloning(generator):
    """Test voice cloning without speaker ID dependency."""
    print("\nTesting single-speaker voice cloning...")
    
    try:
        # Create a simple reference audio (sine wave)
        sample_rate = generator.sample_rate
        duration = 2.0  # 2 seconds
        frequency = 440  # A4 note
        
        t = torch.linspace(0, duration, int(sample_rate * duration))
        reference_audio = 0.3 * torch.sin(2 * torch.pi * frequency * t)
        
        # Create reference segment
        reference_segment = Segment(
            speaker=0,  # This should be ignored now
            text="Hello, this is a reference audio segment.",
            audio=reference_audio
        )
        
        print("Generating audio with single-speaker voice cloning...")
        start_time = time.time()
        
        # Test with different speaker IDs to ensure they're ignored
        for test_speaker_id in [0, 1, 999]:
            print(f"Testing with speaker_id={test_speaker_id} (should be ignored)...")
            
            generated_audio = generator.generate(
                text="This is a test of single-speaker voice cloning.",
                speaker=test_speaker_id,  # This should be ignored
                context=[reference_segment],
                max_audio_length_ms=5000,
                temperature=0.7,
                topk=30,
                stream=False
            )
            
            if generated_audio.numel() > 0:
                print(f"✅ Generated audio with speaker_id={test_speaker_id}: {generated_audio.shape}")
                
                # Save the audio file
                output_file = f"test_output_speaker_{test_speaker_id}.wav"
                torchaudio.save(output_file, generated_audio.unsqueeze(0).cpu(), sample_rate)
                print(f"   Saved to: {output_file}")
            else:
                print(f"❌ No audio generated for speaker_id={test_speaker_id}")
                return False
        
        generation_time = time.time() - start_time
        print(f"✅ Single-speaker voice cloning test completed in {generation_time:.2f}s")
        return True
        
    except Exception as e:
        print(f"❌ Error during voice cloning test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streaming_generation(generator):
    """Test streaming generation with single speaker."""
    print("\nTesting streaming generation...")
    
    try:
        # Create reference audio
        sample_rate = generator.sample_rate
        duration = 1.5
        t = torch.linspace(0, duration, int(sample_rate * duration))
        reference_audio = 0.2 * torch.sin(2 * torch.pi * 220 * t)  # Lower frequency
        
        reference_segment = Segment(
            speaker=0,
            text="This is streaming reference audio.",
            audio=reference_audio
        )
        
        print("Testing streaming generation...")
        generate_streaming_audio(
            generator=generator,
            text="This is a test of streaming generation with single speaker voice cloning.",
            speaker=0,  # Should be ignored
            context=[reference_segment],
            output_file="test_streaming_output.wav",
            max_audio_length_ms=8000,
            temperature=0.7,
            topk=30,
            play_audio=False  # Disable audio playback for testing
        )
        
        print("✅ Streaming generation test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error during streaming test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("Testing Float16 and Single-Speaker Voice Cloning")
    print("=" * 60)
    
    # Test 1: Float16 support
    success, generator = test_float16_support()
    if not success:
        print("\n❌ Float16 test failed. Exiting.")
        return
    
    # Test 2: Single-speaker voice cloning
    success = test_single_speaker_voice_cloning(generator)
    if not success:
        print("\n❌ Single-speaker voice cloning test failed.")
        return
    
    # Test 3: Streaming generation
    success = test_streaming_generation(generator)
    if not success:
        print("\n❌ Streaming generation test failed.")
        return
    
    print("\n" + "=" * 60)
    print("✅ All tests passed successfully!")
    print("✅ Model now uses float16 instead of bfloat16")
    print("✅ Voice cloning works with single speaker (speaker ID ignored)")
    print("=" * 60)

if __name__ == "__main__":
    main()
