"""
Alternative generator with safer torch.compile options to avoid CUDA graph issues.
This version provides a middle ground between performance and stability.
"""

import torch
from generator import *  # Import everything from the original generator

def load_csm_1b_safe_compile(device: str = "cuda") -> Generator:
    """
    Load the CSM-1B model with safer compilation options that avoid CUDA graph issues.
    """
    # Enable CUDA optimizations but be more conservative
    torch.backends.cuda.matmul.allow_tf32 = True
    if hasattr(torch.backends.cuda, 'enable_flash_sdp'):
        torch.backends.cuda.enable_flash_sdp(True)
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.enabled = True
    
    print("Loading CSM-1B model with safe compilation options...")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
    
    model = Model.from_pretrained("sesame/csm-1b")
    
    dtype = torch.float16
    
    # Use safer compilation options
    try:
        # Try compilation with safer settings
        print("Attempting safe model compilation...")
        
        # Use 'default' mode instead of 'reduce-overhead' and disable fullgraph
        model.backbone = torch.compile(
            model.backbone, 
            mode='default',  # Safer than 'reduce-overhead'
            fullgraph=False,  # More flexible
            backend='inductor',
            options={
                "triton.cudagraphs": False,  # Disable CUDA graphs
                "epilogue_fusion": False,   # Disable some aggressive optimizations
                "max_autotune": False       # Disable aggressive autotuning
            }
        )
        
        model.decoder = torch.compile(
            model.decoder, 
            mode='default',
            fullgraph=False,
            backend='inductor',
            options={
                "triton.cudagraphs": False,
                "epilogue_fusion": False,
                "max_autotune": False
            }
        )
        
        print("Safe compilation successful!")
        
    except Exception as e:
        print(f"Safe compilation failed ({e}), falling back to no compilation...")
        # If compilation fails, just use the model without compilation
        pass

    model.to(device=device, dtype=dtype)
    
    print("Model loaded. Creating generator...")
    
    generator = Generator(model)
    generator._stream_buffer_size = 20
    
    # Setup tokenization caching
    generator._tokenization_cache = {}
    
    from functools import lru_cache

    # Patch the tokenize method with caching
    original_tokenize_text = generator._tokenize_text_segment

    @lru_cache(maxsize=2048)
    def cached_tokenize_text_segment(text_str):
        return original_tokenize_text(text_str)

    generator._tokenize_text_segment = lambda text: cached_tokenize_text_segment(text)
    
    # Perform warmup
    warmup_generator(generator)

    return generator

def load_csm_1b_local_safe_compile(model_path: str, device: str = "cuda", audio_num_codebooks: int = 32):
    """
    Load the CSM-1B model from a local checkpoint with safe compilation options.
    """
    import torch
    from functools import lru_cache
    from generator import Generator, Model, ModelArgs

    # Enable CUDA optimizations
    torch.backends.cuda.matmul.allow_tf32 = True
    if hasattr(torch.backends.cuda, 'enable_flash_sdp'):
        torch.backends.cuda.enable_flash_sdp(True)
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.enabled = True

    print(f"Loading CSM-1B model from local checkpoint '{model_path}' with safe compilation...")

    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

    config = ModelArgs(
        backbone_flavor="llama-1B",
        decoder_flavor="llama-100M",
        text_vocab_size=128256,
        audio_vocab_size=2051,
        audio_num_codebooks=audio_num_codebooks,
    )

    model = Model.from_pretrained(model_path)
    model.eval()

    dtype = torch.float16

    # Use safer compilation options
    try:
        print("Attempting safe model compilation...")
        
        model.backbone = torch.compile(
            model.backbone, 
            mode='default',
            fullgraph=False,
            backend='inductor',
            options={
                "triton.cudagraphs": False,
                "epilogue_fusion": False,
                "max_autotune": False
            }
        )
        
        model.decoder = torch.compile(
            model.decoder, 
            mode='default',
            fullgraph=False,
            backend='inductor',
            options={
                "triton.cudagraphs": False,
                "epilogue_fusion": False,
                "max_autotune": False
            }
        )
        
        print("Safe compilation successful!")
        
    except Exception as e:
        print(f"Safe compilation failed ({e}), falling back to no compilation...")

    model.to(device=device, dtype=dtype)

    print("Model loaded. Creating generator...")

    generator = Generator(model)
    generator._stream_buffer_size = 20

    # Setup tokenization caching
    generator._tokenization_cache = {}

    original_tokenize_text = generator._tokenize_text_segment

    @lru_cache(maxsize=2048)
    def cached_tokenize_text_segment(text_str):
        return original_tokenize_text(text_str)

    generator._tokenize_text_segment = lambda text: cached_tokenize_text_segment(text)

    # Perform warmup
    warmup_generator(generator)

    return generator

# Alternative: Completely disable compilation for maximum stability
def load_csm_1b_no_compile(device: str = "cuda") -> Generator:
    """
    Load the CSM-1B model without any compilation for maximum stability.
    """
    torch.backends.cuda.matmul.allow_tf32 = True
    if hasattr(torch.backends.cuda, 'enable_flash_sdp'):
        torch.backends.cuda.enable_flash_sdp(True)
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.enabled = True
    
    print("Loading CSM-1B model without compilation (maximum stability)...")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
    
    model = Model.from_pretrained("sesame/csm-1b")
    dtype = torch.float16
    
    # No compilation at all
    model.to(device=device, dtype=dtype)
    
    print("Model loaded without compilation. Creating generator...")
    
    generator = Generator(model)
    generator._stream_buffer_size = 20
    
    # Setup tokenization caching
    generator._tokenization_cache = {}
    
    from functools import lru_cache

    original_tokenize_text = generator._tokenize_text_segment

    @lru_cache(maxsize=2048)
    def cached_tokenize_text_segment(text_str):
        return original_tokenize_text(text_str)

    generator._tokenize_text_segment = lambda text: cached_tokenize_text_segment(text)
    
    # Perform warmup
    warmup_generator(generator)

    return generator
