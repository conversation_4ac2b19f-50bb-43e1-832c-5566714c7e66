import os
import io
import base64
import tempfile
import time
from typing import Optional, List
import torch
import torchaudio
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.responses import FileResponse, JSONResponse, StreamingResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import logging

# Import the generator and related classes
from generator import Generator, Segment, load_csm_1b, load_csm_1b_local

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Voice Cloning API", description="API for voice cloning using CSM-1B model")

# Global variables
generator: Optional[Generator] = None

class VoiceCloneRequest(BaseModel):
    text: str
    reference_text: str
    reference_audio_base64: Optional[str] = None
    speaker_id: int = 0
    temperature: float = 0.7
    topk: int = 30
    max_audio_length_ms: float = 90000

class VoiceCloneResponse(BaseModel):
    success: bool
    message: str
    audio_base64: Optional[str] = None
    generation_time: Optional[float] = None
    audio_duration: Optional[float] = None

def initialize_model(model_path: Optional[str] = None, device: str = "cuda"):
    """Initialize the CSM-1B model"""
    global generator
    
    try:
        if model_path and os.path.exists(model_path):
            logger.info(f"Loading model from local path: {model_path}")
            generator = load_csm_1b_local(model_path, device)
        else:
            logger.info("Loading model from HuggingFace Hub")
            generator = load_csm_1b(device)
        
        logger.info("Model initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize model: {e}")
        return False

def audio_to_base64(audio_tensor: torch.Tensor, sample_rate: int) -> str:
    """Convert audio tensor to base64 encoded WAV"""
    # Ensure audio is on CPU and in the right format
    audio_cpu = audio_tensor.cpu()
    if audio_cpu.dim() == 1:
        audio_cpu = audio_cpu.unsqueeze(0)
    
    # Create a temporary file to save the audio
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
        torchaudio.save(temp_file.name, audio_cpu, sample_rate)
        
        # Read the file and encode to base64
        with open(temp_file.name, "rb") as f:
            audio_bytes = f.read()
        
        # Clean up temporary file
        os.unlink(temp_file.name)
        
        return base64.b64encode(audio_bytes).decode('utf-8')

def base64_to_audio(base64_str: str, target_sample_rate: int) -> torch.Tensor:
    """Convert base64 encoded audio to tensor"""
    # Decode base64
    audio_bytes = base64.b64decode(base64_str)
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
        temp_file.write(audio_bytes)
        temp_file.flush()
        
        # Load audio
        audio_tensor, sample_rate = torchaudio.load(temp_file.name)
        
        # Clean up
        os.unlink(temp_file.name)
        
        # Resample if necessary
        if sample_rate != target_sample_rate:
            audio_tensor = torchaudio.functional.resample(
                audio_tensor, orig_freq=sample_rate, new_freq=target_sample_rate
            )
        
        # Ensure mono
        if audio_tensor.shape[0] > 1:
            audio_tensor = audio_tensor.mean(dim=0, keepdim=True)
        
        return audio_tensor.squeeze(0)

@app.on_event("startup")
async def startup_event():
    """Initialize the model on startup"""
    success = initialize_model()
    if not success:
        logger.error("Failed to initialize model on startup")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Voice Cloning API", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    model_loaded = generator is not None
    return {
        "status": "healthy" if model_loaded else "unhealthy",
        "model_loaded": model_loaded
    }

@app.post("/clone", response_model=VoiceCloneResponse)
async def clone_voice(request: VoiceCloneRequest):
    """
    Clone voice using reference audio and text
    
    Args:
        request: VoiceCloneRequest containing:
            - text: Text to generate speech for
            - reference_text: Text that corresponds to the reference audio
            - reference_audio_base64: Base64 encoded reference audio (WAV format)
            - speaker_id: Speaker ID (default: 0)
            - temperature: Sampling temperature (default: 0.7)
            - topk: Top-k sampling parameter (default: 30)
            - max_audio_length_ms: Maximum audio length in milliseconds (default: 90000)
    
    Returns:
        VoiceCloneResponse with generated audio as base64 encoded WAV
    """
    if generator is None:
        raise HTTPException(status_code=500, detail="Model not initialized")
    
    if not request.reference_audio_base64:
        raise HTTPException(status_code=400, detail="Reference audio is required")
    
    try:
        start_time = time.time()
        
        # Convert reference audio from base64
        reference_audio = base64_to_audio(request.reference_audio_base64, generator.sample_rate)
        
        # Create reference segment
        reference_segment = Segment(
            speaker=request.speaker_id,
            text=request.reference_text,
            audio=reference_audio
        )
        
        # Generate audio
        generated_audio = generator.generate(
            text=request.text,
            speaker=request.speaker_id,
            context=[reference_segment],
            max_audio_length_ms=request.max_audio_length_ms,
            temperature=request.temperature,
            topk=request.topk,
            stream=False
        )
        
        generation_time = time.time() - start_time
        audio_duration = len(generated_audio) / generator.sample_rate
        
        # Convert to base64
        audio_base64 = audio_to_base64(generated_audio, generator.sample_rate)
        
        return VoiceCloneResponse(
            success=True,
            message="Voice cloning successful",
            audio_base64=audio_base64,
            generation_time=generation_time,
            audio_duration=audio_duration
        )
        
    except Exception as e:
        logger.error(f"Error during voice cloning: {e}")
        return VoiceCloneResponse(
            success=False,
            message=f"Voice cloning failed: {str(e)}"
        )

@app.post("/clone-file")
async def clone_voice_file(
    text: str = Form(...),
    reference_text: str = Form(...),
    reference_audio: UploadFile = File(...),
    speaker_id: int = Form(0),
    temperature: float = Form(0.7),
    topk: int = Form(30),
    max_audio_length_ms: float = Form(90000)
):
    """
    Clone voice using uploaded reference audio file
    
    Args:
        text: Text to generate speech for
        reference_text: Text that corresponds to the reference audio
        reference_audio: Reference audio file (WAV format)
        speaker_id: Speaker ID (default: 0)
        temperature: Sampling temperature (default: 0.7)
        topk: Top-k sampling parameter (default: 30)
        max_audio_length_ms: Maximum audio length in milliseconds (default: 90000)
    
    Returns:
        Generated audio file
    """
    if generator is None:
        raise HTTPException(status_code=500, detail="Model not initialized")
    
    try:
        start_time = time.time()
        
        # Read uploaded file
        audio_bytes = await reference_audio.read()
        
        # Save to temporary file and load
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_file.write(audio_bytes)
            temp_file.flush()
            
            # Load audio
            reference_audio_tensor, sample_rate = torchaudio.load(temp_file.name)
            os.unlink(temp_file.name)
        
        # Resample if necessary
        if sample_rate != generator.sample_rate:
            reference_audio_tensor = torchaudio.functional.resample(
                reference_audio_tensor, orig_freq=sample_rate, new_freq=generator.sample_rate
            )
        
        # Ensure mono
        if reference_audio_tensor.shape[0] > 1:
            reference_audio_tensor = reference_audio_tensor.mean(dim=0, keepdim=True)
        
        reference_audio_tensor = reference_audio_tensor.squeeze(0)
        
        # Create reference segment
        reference_segment = Segment(
            speaker=speaker_id,
            text=reference_text,
            audio=reference_audio_tensor
        )
        
        # Generate audio
        generated_audio = generator.generate(
            text=text,
            speaker=speaker_id,
            context=[reference_segment],
            max_audio_length_ms=max_audio_length_ms,
            temperature=temperature,
            topk=topk,
            stream=False
        )
        
        generation_time = time.time() - start_time
        
        # Save generated audio to temporary file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            torchaudio.save(
                temp_file.name,
                generated_audio.unsqueeze(0).cpu(),
                generator.sample_rate
            )
            
            return FileResponse(
                temp_file.name,
                media_type="audio/wav",
                filename=f"cloned_voice_{int(time.time())}.wav",
                headers={
                    "X-Generation-Time": str(generation_time),
                    "X-Audio-Duration": str(len(generated_audio) / generator.sample_rate)
                }
            )
        
    except Exception as e:
        logger.error(f"Error during voice cloning: {e}")
        raise HTTPException(status_code=500, detail=f"Voice cloning failed: {str(e)}")

@app.post("/generate-voice")
async def generate_voice(
    text: str = Form(..., description="Text to generate speech for"),
    voice_file: UploadFile = File(..., description="Voice sample file (WAV format)"),
    reference_text: Optional[str] = Form(None, description="Text that corresponds to the voice sample (if not provided, will be transcribed)"),
    max_audio_length_ms: Optional[int] = Form(30000, description="Maximum audio length in milliseconds"),
    stream: bool = Form(False, description="Enable streaming response")
):
    """Generate speech using the provided voice sample with optional streaming."""
    if generator is None:
        raise HTTPException(status_code=503, detail="Model not loaded yet")

    if not voice_file.filename.lower().endswith(('.wav', '.mp3', '.flac', '.m4a')):
        raise HTTPException(status_code=400, detail="Voice file must be an audio file (WAV, MP3, FLAC, M4A)")

    try:
        logger.info(f"📥 Received voice sample: {voice_file.filename}")

        # Read uploaded file
        audio_bytes = await voice_file.read()

        # Save to temporary file and load
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_voice_file:
            temp_voice_file.write(audio_bytes)
            temp_voice_file.flush()
            temp_voice_path = temp_voice_file.name

            # Load audio
            reference_audio_tensor, sample_rate = torchaudio.load(temp_voice_path)

        # Use provided reference text or use a default placeholder
        if reference_text:
            voice_text = reference_text
            logger.info(f"📝 Using provided reference text: {voice_text[:50]}...")
        else:
            # If no reference text provided, use a generic placeholder
            voice_text = "This is a voice sample for cloning."
            logger.info(f"📝 Using default reference text: {voice_text}")

        # Resample if necessary
        if sample_rate != generator.sample_rate:
            reference_audio_tensor = torchaudio.functional.resample(
                reference_audio_tensor, orig_freq=sample_rate, new_freq=generator.sample_rate
            )

        # Ensure mono
        if reference_audio_tensor.shape[0] > 1:
            reference_audio_tensor = reference_audio_tensor.mean(dim=0, keepdim=True)

        reference_audio_tensor = reference_audio_tensor.squeeze(0)

        logger.info("🎵 Preparing voice prompt from uploaded file...")
        reference_segment = Segment(
            speaker=0,
            text=voice_text,
            audio=reference_audio_tensor
        )

        logger.info(f"🎵 Generating speech for: {text[:50]}...")
        generation_start = time.time()

        # Clean up temporary file early
        os.unlink(temp_voice_path)

        if stream:
            # Streaming response
            import wave
            import struct

            def generate_streaming_audio():
                # WAV header for streaming
                sample_rate = generator.sample_rate
                channels = 1
                bits_per_sample = 16

                # Prepare WAV header (we'll update the size later)
                wav_header = struct.pack('<4sI4s4sIHHIIHH4sI',
                    b'RIFF', 0, b'WAVE',  # RIFF header
                    b'fmt ', 16, 1, channels, sample_rate,  # fmt chunk
                    sample_rate * channels * bits_per_sample // 8,
                    channels * bits_per_sample // 8, bits_per_sample,
                    b'data', 0  # data chunk header
                )

                yield wav_header

                total_samples = 0

                # Generate audio chunks
                for audio_chunk in generator.generate_stream(
                    text=text,
                    speaker=0,
                    context=[reference_segment],
                    max_audio_length_ms=max_audio_length_ms,
                    temperature=0.7,
                    topk=30
                ):
                    # Convert to 16-bit PCM
                    audio_np = audio_chunk.cpu().numpy()
                    audio_int16 = (audio_np * 32767).astype('int16')
                    total_samples += len(audio_int16)

                    # Yield audio data
                    yield audio_int16.tobytes()

                logger.info(f"✅ Streaming completed with {total_samples} samples")

            return StreamingResponse(
                generate_streaming_audio(),
                media_type="audio/wav",
                headers={
                    "Content-Disposition": f"attachment; filename=generated_speech_stream_{int(time.time())}.wav",
                    "X-Streaming": "true"
                }
            )
        else:
            # Non-streaming response (original behavior)
            audio_tensor = generator.generate(
                text=text,
                speaker=0,
                context=[reference_segment],
                max_audio_length_ms=max_audio_length_ms,
            )

            generation_time = time.time() - generation_start
            logger.info(f"✅ Speech generated in {generation_time:.2f} seconds")

            # Save generated audio to temporary file and return
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_output_file:
                torchaudio.save(
                    temp_output_file.name,
                    audio_tensor.unsqueeze(0).cpu(),
                    generator.sample_rate
                )

                return FileResponse(
                    temp_output_file.name,
                    media_type="audio/wav",
                    filename=f"generated_speech_{int(time.time())}.wav",
                    headers={"X-Generation-Time": str(generation_time)}
                )

    except Exception as e:
        logger.exception(f"❌ Error during voice generation")
        if 'temp_voice_path' in locals() and os.path.exists(temp_voice_path):
            os.unlink(temp_voice_path)
        raise HTTPException(status_code=500, detail=f"Error generating speech: {str(e)}")

@app.get("/ui", response_class=HTMLResponse)
async def get_ui():
    """Simple UI for testing voice generation with streaming"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Voice Cloning Streaming Test</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
            button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background-color: #0056b3; }
            button:disabled { background-color: #6c757d; cursor: not-allowed; }
            .status { margin-top: 20px; padding: 10px; border-radius: 4px; }
            .status.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .status.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .status.info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
            .audio-player { margin-top: 20px; }
            .progress { width: 100%; height: 20px; background-color: #f0f0f0; border-radius: 10px; overflow: hidden; margin-top: 10px; }
            .progress-bar { height: 100%; background-color: #007bff; width: 0%; transition: width 0.3s ease; }
        </style>
    </head>
    <body>
        <h1>🎤 Voice Cloning Streaming Test</h1>

        <form id="voiceForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="text">Text to Generate:</label>
                <textarea id="text" name="text" rows="3" placeholder="Enter the text you want to generate speech for..." required>Hello, this is a test of voice cloning with streaming capabilities!</textarea>
            </div>

            <div class="form-group">
                <label for="voice_file">Voice Sample File:</label>
                <input type="file" id="voice_file" name="voice_file" accept=".wav,.mp3,.flac,.m4a" required>
                <small>Upload a clear voice sample (WAV, MP3, FLAC, or M4A format)</small>
            </div>

            <div class="form-group">
                <label for="reference_text">Reference Text (Optional):</label>
                <textarea id="reference_text" name="reference_text" rows="2" placeholder="What does the person say in the voice sample? (Leave empty for auto-detection)"></textarea>
            </div>

            <div class="form-group">
                <label for="max_audio_length_ms">Max Audio Length (ms):</label>
                <input type="number" id="max_audio_length_ms" name="max_audio_length_ms" value="30000" min="1000" max="90000">
            </div>

            <div class="form-group">
                <label for="stream">Streaming Mode:</label>
                <select id="stream" name="stream">
                    <option value="true">Enable Streaming (Real-time)</option>
                    <option value="false">Disable Streaming (Wait for complete)</option>
                </select>
            </div>

            <button type="submit" id="generateBtn">🎵 Generate Voice</button>
        </form>

        <div id="status"></div>
        <div id="progress" class="progress" style="display: none;">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        <div id="audioPlayer" class="audio-player"></div>

        <script>
            document.getElementById('voiceForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const generateBtn = document.getElementById('generateBtn');
                const status = document.getElementById('status');
                const progress = document.getElementById('progress');
                const progressBar = document.getElementById('progressBar');
                const audioPlayer = document.getElementById('audioPlayer');

                // Reset UI
                generateBtn.disabled = true;
                generateBtn.textContent = '🔄 Generating...';
                status.innerHTML = '';
                audioPlayer.innerHTML = '';

                const isStreaming = formData.get('stream') === 'true';

                if (isStreaming) {
                    progress.style.display = 'block';
                    status.innerHTML = '<div class="status info">🎵 Starting streaming generation...</div>';

                    try {
                        const response = await fetch('/generate-voice', {
                            method: 'POST',
                            body: formData
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const reader = response.body.getReader();
                        const chunks = [];
                        let receivedLength = 0;

                        status.innerHTML = '<div class="status info">📡 Receiving streaming audio...</div>';

                        while (true) {
                            const { done, value } = await reader.read();

                            if (done) break;

                            chunks.push(value);
                            receivedLength += value.length;

                            // Update progress (approximate)
                            const progress = Math.min((receivedLength / 100000) * 100, 90);
                            progressBar.style.width = progress + '%';
                        }

                        progressBar.style.width = '100%';

                        // Combine chunks and create audio
                        const audioBlob = new Blob(chunks, { type: 'audio/wav' });
                        const audioUrl = URL.createObjectURL(audioBlob);

                        audioPlayer.innerHTML = `
                            <h3>🎵 Generated Audio (Streaming):</h3>
                            <audio controls style="width: 100%;">
                                <source src="${audioUrl}" type="audio/wav">
                                Your browser does not support the audio element.
                            </audio>
                            <br><br>
                            <a href="${audioUrl}" download="generated_voice_stream.wav">
                                <button>💾 Download Audio</button>
                            </a>
                        `;

                        status.innerHTML = '<div class="status success">✅ Streaming generation completed!</div>';

                    } catch (error) {
                        status.innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
                    }
                } else {
                    status.innerHTML = '<div class="status info">⏳ Generating complete audio (non-streaming)...</div>';

                    try {
                        const response = await fetch('/generate-voice', {
                            method: 'POST',
                            body: formData
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const audioBlob = await response.blob();
                        const audioUrl = URL.createObjectURL(audioBlob);

                        audioPlayer.innerHTML = `
                            <h3>🎵 Generated Audio (Complete):</h3>
                            <audio controls style="width: 100%;">
                                <source src="${audioUrl}" type="audio/wav">
                                Your browser does not support the audio element.
                            </audio>
                            <br><br>
                            <a href="${audioUrl}" download="generated_voice.wav">
                                <button>💾 Download Audio</button>
                            </a>
                        `;

                        const generationTime = response.headers.get('X-Generation-Time');
                        status.innerHTML = `<div class="status success">✅ Generation completed! ${generationTime ? `(${parseFloat(generationTime).toFixed(2)}s)` : ''}</div>`;

                    } catch (error) {
                        status.innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
                    }
                }

                // Reset button
                generateBtn.disabled = false;
                generateBtn.textContent = '🎵 Generate Voice';
                progress.style.display = 'none';
                progressBar.style.width = '0%';
            });
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
