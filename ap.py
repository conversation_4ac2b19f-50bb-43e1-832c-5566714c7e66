import os
import io
import base64
import tempfile
import time
from typing import Optional, List
import torch
import torchaudio
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
import logging

# Import the generator and related classes
from generator import Generator, Segment, load_csm_1b, load_csm_1b_local

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Voice Cloning API", description="API for voice cloning using CSM-1B model")

# Global variables
generator: Optional[Generator] = None

class VoiceCloneRequest(BaseModel):
    text: str
    reference_text: str
    reference_audio_base64: Optional[str] = None
    speaker_id: int = 0
    temperature: float = 0.7
    topk: int = 30
    max_audio_length_ms: float = 90000

class VoiceCloneResponse(BaseModel):
    success: bool
    message: str
    audio_base64: Optional[str] = None
    generation_time: Optional[float] = None
    audio_duration: Optional[float] = None

def initialize_model(model_path: Optional[str] = None, device: str = "cuda"):
    """Initialize the CSM-1B model"""
    global generator
    
    try:
        if model_path and os.path.exists(model_path):
            logger.info(f"Loading model from local path: {model_path}")
            generator = load_csm_1b_local(model_path, device)
        else:
            logger.info("Loading model from HuggingFace Hub")
            generator = load_csm_1b(device)
        
        logger.info("Model initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize model: {e}")
        return False

def audio_to_base64(audio_tensor: torch.Tensor, sample_rate: int) -> str:
    """Convert audio tensor to base64 encoded WAV"""
    # Ensure audio is on CPU and in the right format
    audio_cpu = audio_tensor.cpu()
    if audio_cpu.dim() == 1:
        audio_cpu = audio_cpu.unsqueeze(0)
    
    # Create a temporary file to save the audio
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
        torchaudio.save(temp_file.name, audio_cpu, sample_rate)
        
        # Read the file and encode to base64
        with open(temp_file.name, "rb") as f:
            audio_bytes = f.read()
        
        # Clean up temporary file
        os.unlink(temp_file.name)
        
        return base64.b64encode(audio_bytes).decode('utf-8')

def base64_to_audio(base64_str: str, target_sample_rate: int) -> torch.Tensor:
    """Convert base64 encoded audio to tensor"""
    # Decode base64
    audio_bytes = base64.b64decode(base64_str)
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
        temp_file.write(audio_bytes)
        temp_file.flush()
        
        # Load audio
        audio_tensor, sample_rate = torchaudio.load(temp_file.name)
        
        # Clean up
        os.unlink(temp_file.name)
        
        # Resample if necessary
        if sample_rate != target_sample_rate:
            audio_tensor = torchaudio.functional.resample(
                audio_tensor, orig_freq=sample_rate, new_freq=target_sample_rate
            )
        
        # Ensure mono
        if audio_tensor.shape[0] > 1:
            audio_tensor = audio_tensor.mean(dim=0, keepdim=True)
        
        return audio_tensor.squeeze(0)

@app.on_event("startup")
async def startup_event():
    """Initialize the model on startup"""
    success = initialize_model()
    if not success:
        logger.error("Failed to initialize model on startup")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Voice Cloning API", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    model_loaded = generator is not None
    return {
        "status": "healthy" if model_loaded else "unhealthy",
        "model_loaded": model_loaded
    }

@app.post("/clone", response_model=VoiceCloneResponse)
async def clone_voice(request: VoiceCloneRequest):
    """
    Clone voice using reference audio and text
    
    Args:
        request: VoiceCloneRequest containing:
            - text: Text to generate speech for
            - reference_text: Text that corresponds to the reference audio
            - reference_audio_base64: Base64 encoded reference audio (WAV format)
            - speaker_id: Speaker ID (default: 0)
            - temperature: Sampling temperature (default: 0.7)
            - topk: Top-k sampling parameter (default: 30)
            - max_audio_length_ms: Maximum audio length in milliseconds (default: 90000)
    
    Returns:
        VoiceCloneResponse with generated audio as base64 encoded WAV
    """
    if generator is None:
        raise HTTPException(status_code=500, detail="Model not initialized")
    
    if not request.reference_audio_base64:
        raise HTTPException(status_code=400, detail="Reference audio is required")
    
    try:
        start_time = time.time()
        
        # Convert reference audio from base64
        reference_audio = base64_to_audio(request.reference_audio_base64, generator.sample_rate)
        
        # Create reference segment
        reference_segment = Segment(
            speaker=request.speaker_id,
            text=request.reference_text,
            audio=reference_audio
        )
        
        # Generate audio
        generated_audio = generator.generate(
            text=request.text,
            speaker=request.speaker_id,
            context=[reference_segment],
            max_audio_length_ms=request.max_audio_length_ms,
            temperature=request.temperature,
            topk=request.topk,
            stream=False
        )
        
        generation_time = time.time() - start_time
        audio_duration = len(generated_audio) / generator.sample_rate
        
        # Convert to base64
        audio_base64 = audio_to_base64(generated_audio, generator.sample_rate)
        
        return VoiceCloneResponse(
            success=True,
            message="Voice cloning successful",
            audio_base64=audio_base64,
            generation_time=generation_time,
            audio_duration=audio_duration
        )
        
    except Exception as e:
        logger.error(f"Error during voice cloning: {e}")
        return VoiceCloneResponse(
            success=False,
            message=f"Voice cloning failed: {str(e)}"
        )

@app.post("/clone-file")
async def clone_voice_file(
    text: str = Form(...),
    reference_text: str = Form(...),
    reference_audio: UploadFile = File(...),
    speaker_id: int = Form(0),
    temperature: float = Form(0.7),
    topk: int = Form(30),
    max_audio_length_ms: float = Form(90000)
):
    """
    Clone voice using uploaded reference audio file
    
    Args:
        text: Text to generate speech for
        reference_text: Text that corresponds to the reference audio
        reference_audio: Reference audio file (WAV format)
        speaker_id: Speaker ID (default: 0)
        temperature: Sampling temperature (default: 0.7)
        topk: Top-k sampling parameter (default: 30)
        max_audio_length_ms: Maximum audio length in milliseconds (default: 90000)
    
    Returns:
        Generated audio file
    """
    if generator is None:
        raise HTTPException(status_code=500, detail="Model not initialized")
    
    try:
        start_time = time.time()
        
        # Read uploaded file
        audio_bytes = await reference_audio.read()
        
        # Save to temporary file and load
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_file.write(audio_bytes)
            temp_file.flush()
            
            # Load audio
            reference_audio_tensor, sample_rate = torchaudio.load(temp_file.name)
            os.unlink(temp_file.name)
        
        # Resample if necessary
        if sample_rate != generator.sample_rate:
            reference_audio_tensor = torchaudio.functional.resample(
                reference_audio_tensor, orig_freq=sample_rate, new_freq=generator.sample_rate
            )
        
        # Ensure mono
        if reference_audio_tensor.shape[0] > 1:
            reference_audio_tensor = reference_audio_tensor.mean(dim=0, keepdim=True)
        
        reference_audio_tensor = reference_audio_tensor.squeeze(0)
        
        # Create reference segment
        reference_segment = Segment(
            speaker=speaker_id,
            text=reference_text,
            audio=reference_audio_tensor
        )
        
        # Generate audio
        generated_audio = generator.generate(
            text=text,
            speaker=speaker_id,
            context=[reference_segment],
            max_audio_length_ms=max_audio_length_ms,
            temperature=temperature,
            topk=topk,
            stream=False
        )
        
        generation_time = time.time() - start_time
        
        # Save generated audio to temporary file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            torchaudio.save(
                temp_file.name,
                generated_audio.unsqueeze(0).cpu(),
                generator.sample_rate
            )
            
            return FileResponse(
                temp_file.name,
                media_type="audio/wav",
                filename=f"cloned_voice_{int(time.time())}.wav",
                headers={
                    "X-Generation-Time": str(generation_time),
                    "X-Audio-Duration": str(len(generated_audio) / generator.sample_rate)
                }
            )
        
    except Exception as e:
        logger.error(f"Error during voice cloning: {e}")
        raise HTTPException(status_code=500, detail=f"Voice cloning failed: {str(e)}")

@app.post("/generate-voice")
async def generate_voice(
    text: str = Form(..., description="Text to generate speech for"),
    voice_file: UploadFile = File(..., description="Voice sample file (WAV format)"),
    reference_text: Optional[str] = Form(None, description="Text that corresponds to the voice sample (if not provided, will be transcribed)"),
    max_audio_length_ms: Optional[int] = Form(30000, description="Maximum audio length in milliseconds")
):
    """Generate speech using the provided voice sample."""
    if generator is None:
        raise HTTPException(status_code=503, detail="Model not loaded yet")

    if not voice_file.filename.lower().endswith(('.wav', '.mp3', '.flac', '.m4a')):
        raise HTTPException(status_code=400, detail="Voice file must be an audio file (WAV, MP3, FLAC, M4A)")

    try:
        logger.info(f"📥 Received voice sample: {voice_file.filename}")

        # Read uploaded file
        audio_bytes = await voice_file.read()

        # Save to temporary file and load
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_voice_file:
            temp_voice_file.write(audio_bytes)
            temp_voice_file.flush()
            temp_voice_path = temp_voice_file.name

            # Load audio
            reference_audio_tensor, sample_rate = torchaudio.load(temp_voice_path)

        # Use provided reference text or use a default placeholder
        if reference_text:
            voice_text = reference_text
            logger.info(f"📝 Using provided reference text: {voice_text[:50]}...")
        else:
            # If no reference text provided, use a generic placeholder
            # In a real implementation, you might want to use speech-to-text here
            voice_text = "This is a voice sample for cloning."
            logger.info(f"📝 Using default reference text: {voice_text}")

        # Resample if necessary
        if sample_rate != generator.sample_rate:
            reference_audio_tensor = torchaudio.functional.resample(
                reference_audio_tensor, orig_freq=sample_rate, new_freq=generator.sample_rate
            )

        # Ensure mono
        if reference_audio_tensor.shape[0] > 1:
            reference_audio_tensor = reference_audio_tensor.mean(dim=0, keepdim=True)

        reference_audio_tensor = reference_audio_tensor.squeeze(0)

        logger.info("🎵 Preparing voice prompt from uploaded file...")
        reference_segment = Segment(
            speaker=0,
            text=voice_text,
            audio=reference_audio_tensor
        )

        logger.info(f"🎵 Generating speech for: {text[:50]}...")
        generation_start = time.time()

        audio_tensor = generator.generate(
            text=text,
            speaker=0,
            context=[reference_segment],
            max_audio_length_ms=max_audio_length_ms,
        )

        generation_time = time.time() - generation_start
        logger.info(f"✅ Speech generated in {generation_time:.2f} seconds")

        # Clean up temporary file
        os.unlink(temp_voice_path)

        # Save generated audio to temporary file and return
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_output_file:
            torchaudio.save(
                temp_output_file.name,
                audio_tensor.unsqueeze(0).cpu(),
                generator.sample_rate
            )

            return FileResponse(
                temp_output_file.name,
                media_type="audio/wav",
                filename=f"generated_speech_{int(time.time())}.wav",
                headers={"X-Generation-Time": str(generation_time)}
            )

    except Exception as e:
        logger.exception(f"❌ Error during voice generation")
        if 'temp_voice_path' in locals() and os.path.exists(temp_voice_path):
            os.unlink(temp_voice_path)
        raise HTTPException(status_code=500, detail=f"Error generating speech: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
